const React = require('react');
const { useState, useEffect } = React;
const { HashRouter: Router, Routes, Route, Navigate } = require('react-router-dom');

// استيراد السياقات
const { AuthProvider } = require('./contexts/AuthContext');
const { SettingsProvider } = require('./contexts/SettingsContext');
const { NotificationsProvider } = require('./contexts/NotificationsContext');

// استيراد المكونات
const Navbar = require('./components/layout/Navbar');
const Login = require('./pages/auth/Login');
const Dashboard = require('./pages/Dashboard');
const NotFound = require('./pages/NotFound');

// استيراد صفحات الطلبات
const OrdersList = require('./pages/orders/OrdersList');
const OrderDetails = require('./pages/orders/OrderDetails');
const OrderForm = require('./pages/orders/OrderForm');
const CustomOrderForm = require('./pages/orders/CustomOrderForm');
const ProductionTracker = require('./pages/orders/ProductionTracker');

// استيراد الصفحات المتقدمة
const MaterialReservations = require('./pages/materials/MaterialReservations');
const QualityControl = require('./pages/quality/QualityControl');
const ProductionReports = require('./pages/reports/ProductionReports');

// استيراد صفحات العملاء
const CustomersList = require('./pages/customers/CustomersList');
const CustomerDetails = require('./pages/customers/CustomerDetails');
const CustomerForm = require('./pages/customers/CustomerForm');



// استيراد صفحات المواد الخام
const MaterialsList = require('./pages/materials/MaterialsList');
const MaterialDetails = require('./pages/materials/MaterialDetails');
const MaterialForm = require('./pages/materials/MaterialForm');

// استيراد صفحات المخزون
const InventoryList = require('./pages/inventory/InventoryList');
const InventoryDashboard = require('./pages/inventory/InventoryDashboard');
const InventoryForm = require('./pages/inventory/InventoryForm');

// استيراد صفحات الفونير
const VeneerList = require('./pages/veneer/VeneerList');

// استيراد صفحات العمال
const WorkersList = require('./pages/workers/WorkersList');
const WorkerDetails = require('./pages/workers/WorkerDetails');
const WorkerForm = require('./pages/workers/WorkerForm');

// استيراد صفحات المرتبات
const PayrollDashboard = require('./pages/payroll/PayrollDashboard');
const AdvancesManagement = require('./pages/payroll/AdvancesManagement');
const MonthlyPayrolls = require('./pages/payroll/MonthlyPayrolls');

// استيراد الصفحات المدموجة الجديدة
const ProductionManagement = require('./pages/production/ProductionManagement');
const HumanResources = require('./pages/hr/HumanResources');
const FinancialManagement = require('./pages/financial/FinancialManagement');

// استيراد صفحات المصنع والتكاليف
const ExpensesList = require('./pages/expenses/ExpensesList');
const ExpenseForm = require('./pages/expenses/ExpenseForm');

// استيراد صفحات الفواتير
const InvoicesList = require('./pages/invoices/InvoicesList');
const InvoiceDetails = require('./pages/invoices/InvoiceDetails');
const InvoiceForm = require('./pages/invoices/InvoiceForm');

// استيراد صفحات التقارير
const SalesReport = require('./pages/reports/SalesReport');
const ExpensesReport = require('./pages/reports/ExpensesReport');
const ProfitReport = require('./pages/reports/ProfitReport');
const WorkersReport = require('./pages/reports/WorkersReport');
const MaterialsReport = require('./pages/reports/MaterialsReport');


// استيراد صفحات الإعدادات
const Settings = require('./pages/settings/Settings');
const UsersList = require('./pages/settings/UsersList');
const UserForm = require('./pages/settings/UserForm');
const BackupRestore = require('./pages/settings/BackupRestore');

// مكون الحماية للمسارات
const ProtectedRoute = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(true); // مؤقتاً نعتبر المستخدم مسجل الدخول

  return isAuthenticated ? children : React.createElement(Navigate, { to: '/login' });
};

// تخطيط الصفحة مع الشريط العلوي الجديد
const Layout = ({ children }) => {
  return React.createElement('div', { className: 'app-container' },
    React.createElement(Navbar),
    React.createElement('div', { className: 'content-container' },
      React.createElement('main', { className: 'main-content' },
        children
      )
    )
  );
};

// المكون الرئيسي للتطبيق
const App = () => {
  return React.createElement(AuthProvider, null,
    React.createElement(SettingsProvider, null,
      React.createElement(NotificationsProvider, null,
        React.createElement(Router, null,
          React.createElement('div', { className: 'app' },
            React.createElement(Routes, null,
              // صفحة تسجيل الدخول
              React.createElement(Route, {
                path: '/login',
                element: React.createElement(Login)
              }),

              // الصفحة الرئيسية
              React.createElement(Route, {
                path: '/',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(Dashboard)
                  )
                )
              }),


              // مسارات الطلبات
              React.createElement(Route, {
                path: '/orders',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(OrdersList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(OrderForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(OrderDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(OrderForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/custom/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomOrderForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/custom/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomOrderForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/production/:orderId',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductionTracker)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/quality/:orderId',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(QualityControl)
                  )
                )
              }),

              // مسارات العملاء
              React.createElement(Route, {
                path: '/customers',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomersList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/customers/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomerForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/customers/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomerDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/customers/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomerForm)
                  )
                )
              }),

              // مسارات المواد الخام
              React.createElement(Route, {
                path: '/materials',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialsList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/materials/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/materials/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/materials/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/materials/reservations',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialReservations)
                  )
                )
              }),

              // مسارات المخزون
              React.createElement(Route, {
                path: '/inventory',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InventoryDashboard)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/inventory/list',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InventoryList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/inventory/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InventoryForm)
                  )
                )
              }),

              // مسارات الفونير
              React.createElement(Route, {
                path: '/veneer',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(VeneerList)
                  )
                )
              }),

              // مسارات العمال
              React.createElement(Route, {
                path: '/workers',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkersList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/workers/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkerForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/workers/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkerDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/workers/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkerForm)
                  )
                )
              }),

              // مسارات المرتبات
              React.createElement(Route, {
                path: '/payroll',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(PayrollDashboard)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/payroll/monthly',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MonthlyPayrolls)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/payroll/advances',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(AdvancesManagement)
                  )
                )
              }),

              // المسارات الجديدة المدموجة مع المسارات الفرعية
              React.createElement(Route, {
                path: '/production',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductionManagement)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/production/:section',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductionManagement)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/hr',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(HumanResources)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/hr/:section',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(HumanResources)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/financial',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(FinancialManagement)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/financial/:section',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(FinancialManagement)
                  )
                )
              }),

              // مسارات المصروفات
              React.createElement(Route, {
                path: '/expenses',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ExpensesList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/expenses/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ExpenseForm)
                  )
                )
              }),

              // مسارات الفواتير
              React.createElement(Route, {
                path: '/invoices',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InvoicesList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/invoices/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InvoiceForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/invoices/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InvoiceDetails)
                  )
                )
              }),

              // مسارات التقارير
              React.createElement(Route, {
                path: '/reports',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(SalesReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/sales',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(SalesReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/expenses',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ExpensesReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/profit',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProfitReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/workers',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkersReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/materials',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialsReport)
                  )
                )
              }),

              React.createElement(Route, {
                path: '/reports/production',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductionReports)
                  )
                )
              }),

              // مسارات الإعدادات
              React.createElement(Route, {
                path: '/settings',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(Settings)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/settings/users',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(UsersList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/settings/users/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(UserForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/settings/backup',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(BackupRestore)
                  )
                )
              }),

              // صفحة غير موجودة
              React.createElement(Route, {
                path: '*',
                element: React.createElement(NotFound)
              })
            )
          )
        )
      )
    )
  );
};

module.exports = App;
